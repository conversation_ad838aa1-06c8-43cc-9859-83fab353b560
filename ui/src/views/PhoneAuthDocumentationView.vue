<script lang="ts" setup>
import { ref } from 'vue'
import Card from 'primevue/card'
import Accordion from 'primevue/accordion'
import AccordionPanel from 'primevue/accordionpanel'
import AccordionHeader from 'primevue/accordionheader'
import AccordionContent from 'primevue/accordioncontent'
import Badge from 'primevue/badge'
import Divider from 'primevue/divider'
import {
  PhoneIcon,
  GlobeAltIcon,
  KeyIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  SpeakerWaveIcon,
  HashtagIcon,
} from '@heroicons/vue/24/outline'

const phoneNumber = ref('(*************') // This would typically come from configuration
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
    <!-- Header Section -->
    <div class="relative overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-r from-emerald-600 via-emerald-500 to-teal-500"></div>

      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
          <div class="flex items-center justify-center mb-6">
            <PhoneIcon class="h-16 w-16 text-white animate-bounce-gentle" />
          </div>

          <h1 class="text-5xl md:text-6xl font-bold text-white mb-4 tracking-tight">Test Schedule Phone Line</h1>

          <p class="text-xl text-white max-w-3xl mx-auto">
            Call our automated phone system to get information about your upcoming test schedules. Follow these simple steps to verify your identity and receive your schedule information.
          </p>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="py-16 px-4 sm:px-6 lg:px-8">
      <div class="max-w-6xl mx-auto space-y-8">
        <!-- Quick Start Card -->
        <Card
          :pt="{
            root: 'bg-white shadow-lg border border-emerald-100',
            header: 'bg-emerald-50 border-b border-emerald-100',
            content: 'p-6',
          }"
        >
          <template #header>
            <div class="p-6">
              <div class="flex items-center gap-3">
                <div class="w-12 h-12 bg-emerald-100 rounded-xl flex items-center justify-center">
                  <PhoneIcon class="h-6 w-6 text-emerald-600" />
                </div>
                <div>
                  <h2 class="text-2xl font-bold text-slate-900">Quick Start</h2>
                  <p class="text-slate-600">Get your test schedule information in just a few simple steps</p>
                </div>
              </div>
            </div>
          </template>

          <template #content>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div class="text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span class="text-2xl font-bold text-blue-600">1</span>
                </div>
                <h3 class="text-lg font-semibold text-slate-900 mb-2">Call the Number</h3>
                <p class="text-slate-600 text-sm">Dial {{ phoneNumber }} from your registered phone</p>
              </div>

              <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span class="text-2xl font-bold text-green-600">2</span>
                </div>
                <h3 class="text-lg font-semibold text-slate-900 mb-2">Select Language</h3>
                <p class="text-slate-600 text-sm">Press 1 for English or 2 for Spanish</p>
              </div>

              <div class="text-center">
                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span class="text-2xl font-bold text-purple-600">3</span>
                </div>
                <h3 class="text-lg font-semibold text-slate-900 mb-2">Enter PIN</h3>
                <p class="text-slate-600 text-sm">Enter your 4-digit PIN followed by # to get your schedule</p>
              </div>
            </div>

            <div class="mt-8 p-4 bg-emerald-50 rounded-lg border border-emerald-200">
              <div class="flex items-start gap-3">
                <InformationCircleIcon class="h-5 w-5 text-emerald-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p class="text-emerald-800 font-medium">Important:</p>
                  <p class="text-emerald-700 text-sm">You must call from the phone number registered in your patient account.</p>
                </div>
              </div>
            </div>
          </template>
        </Card>

        <!-- Detailed Instructions -->
        <Card
          :pt="{
            root: 'bg-white shadow-lg',
            content: 'p-6',
          }"
        >
          <template #content>
            <div class="mb-6">
              <h2 class="text-2xl font-bold text-slate-900 mb-2">Detailed Step-by-Step Instructions</h2>
              <p class="text-slate-600">Follow these detailed instructions to get your test schedule information</p>
            </div>

            <div class="space-y-6">
              <!-- Step 1 -->
              <div class="flex gap-4">
                <div class="flex-shrink-0">
                  <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <PhoneIcon class="h-5 w-5 text-blue-600" />
                  </div>
                </div>
                <div class="flex-1">
                  <h3 class="text-lg font-semibold text-slate-900 mb-2">Step 1: Make the Call</h3>
                  <p class="text-slate-600 mb-3">
                    Using the phone number registered in your patient account, dial <strong>{{ phoneNumber }}</strong>
                  </p>
                  <div class="bg-blue-50 p-3 rounded-lg border border-blue-200">
                    <p class="text-blue-800 text-sm">
                      <strong>What you'll hear:</strong> "Welcome to Emerald App. For English, press 1. Para español, presione 2."
                    </p>
                  </div>
                </div>
              </div>

              <!-- Step 2 -->
              <div class="flex gap-4">
                <div class="flex-shrink-0">
                  <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <GlobeAltIcon class="h-5 w-5 text-green-600" />
                  </div>
                </div>
                <div class="flex-1">
                  <h3 class="text-lg font-semibold text-slate-900 mb-2">Step 2: Choose Your Language</h3>
                  <p class="text-slate-600 mb-3">You have 6 seconds to select your preferred language:</p>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div class="bg-green-50 p-3 rounded-lg border border-green-200">
                      <div class="flex items-center gap-2">
                        <Badge value="1" severity="success" />
                        <span class="text-green-800 font-medium">English</span>
                      </div>
                    </div>
                    <div class="bg-green-50 p-3 rounded-lg border border-green-200">
                      <div class="flex items-center gap-2">
                        <Badge value="2" severity="success" />
                        <span class="text-green-800 font-medium">Spanish (Español)</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Step 3 -->
              <div class="flex gap-4">
                <div class="flex-shrink-0">
                  <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                    <KeyIcon class="h-5 w-5 text-purple-600" />
                  </div>
                </div>
                <div class="flex-1">
                  <h3 class="text-lg font-semibold text-slate-900 mb-2">Step 3: Enter Your PIN</h3>
                  <p class="text-slate-600 mb-3">
                    After language selection, you'll be prompted to enter your 4-digit PIN followed by the # (pound) key to verify your identity.
                  </p>
                  <div class="bg-purple-50 p-3 rounded-lg border border-purple-200">
                    <div class="flex items-center gap-2 mb-2">
                      <ClockIcon class="h-4 w-4 text-purple-600" />
                      <span class="text-purple-800 font-medium">You have 10 seconds to enter your PIN</span>
                    </div>
                    <div class="flex items-center gap-2">
                      <HashtagIcon class="h-4 w-4 text-purple-600" />
                      <span class="text-purple-800 text-sm">Don't forget to press # after your 4-digit PIN</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Step 4 -->
              <div class="flex gap-4">
                <div class="flex-shrink-0">
                  <div class="w-10 h-10 bg-emerald-100 rounded-full flex items-center justify-center">
                    <CheckCircleIcon class="h-5 w-5 text-emerald-600" />
                  </div>
                </div>
                <div class="flex-1">
                  <h3 class="text-lg font-semibold text-slate-900 mb-2">Step 4: Receive Your Schedule Information</h3>
                  <p class="text-slate-600 mb-3">
                    Upon successful PIN verification, you'll hear a personalized confirmation message and your test schedule information.
                  </p>
                  <div class="bg-emerald-50 p-3 rounded-lg border border-emerald-200">
                    <p class="text-emerald-800 text-sm">
                      <strong>Current message:</strong> "Hello [Your Name]. Your identity has been verified. Thank you for calling."<br />
                      <strong>Note:</strong> Schedule information will be provided here once the scheduling system is implemented.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </Card>

        <!-- Important Information -->
        <Card
          :pt="{
            root: 'bg-white shadow-lg',
            content: 'p-6',
          }"
        >
          <template #content>
            <div class="mb-6">
              <h2 class="text-2xl font-bold text-slate-900 mb-2">Important Information</h2>
              <p class="text-slate-600">Key details you should know about the test schedule phone system</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="space-y-4">
                <div class="flex items-start gap-3">
                  <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <ExclamationTriangleIcon class="h-4 w-4 text-red-600" />
                  </div>
                  <div>
                    <h4 class="font-semibold text-slate-900">Maximum Attempts</h4>
                    <p class="text-slate-600 text-sm">You have a maximum of 3 attempts to enter your PIN correctly. After 3 failed attempts, the call will automatically end.</p>
                  </div>
                </div>

                <div class="flex items-start gap-3">
                  <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <PhoneIcon class="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <h4 class="font-semibold text-slate-900">Registered Phone Only</h4>
                    <p class="text-slate-600 text-sm">You must call from the phone number that is registered in your patient account. Calls from other numbers will not be recognized.</p>
                  </div>
                </div>

                <div class="flex items-start gap-3">
                  <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <KeyIcon class="h-4 w-4 text-purple-600" />
                  </div>
                  <div>
                    <h4 class="font-semibold text-slate-900">4-Digit PIN</h4>
                    <p class="text-slate-600 text-sm">Your PIN is exactly 4 digits long. Always press the # (pound) key after entering your PIN to confirm your entry.</p>
                  </div>
                </div>
              </div>

              <div class="space-y-4">
                <div class="flex items-start gap-3">
                  <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <GlobeAltIcon class="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <h4 class="font-semibold text-slate-900">Bilingual Support</h4>
                    <p class="text-slate-600 text-sm">The system supports both English and Spanish. Your language choice will be remembered for the duration of your call.</p>
                  </div>
                </div>

                <div class="flex items-start gap-3">
                  <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <ClockIcon class="h-4 w-4 text-yellow-600" />
                  </div>
                  <div>
                    <h4 class="font-semibold text-slate-900">Time Limits</h4>
                    <p class="text-slate-600 text-sm">Language selection: 6 seconds<br />PIN entry: 10 seconds<br />If no input is received, the call will end automatically.</p>
                  </div>
                </div>

                <div class="flex items-start gap-3">
                  <div class="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <SpeakerWaveIcon class="h-4 w-4 text-emerald-600" />
                  </div>
                  <div>
                    <h4 class="font-semibold text-slate-900">Clear Audio</h4>
                    <p class="text-slate-600 text-sm">Ensure you're in a quiet environment with good phone reception for the best experience with voice prompts.</p>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </Card>

        <!-- Troubleshooting -->
        <Card
          :pt="{
            root: 'bg-white shadow-lg',
            content: 'p-6',
          }"
        >
          <template #content>
            <div class="mb-6">
              <h2 class="text-2xl font-bold text-slate-900 mb-2">Troubleshooting</h2>
              <p class="text-slate-600">Common issues and their solutions</p>
            </div>

            <Accordion>
              <AccordionPanel value="0">
                <AccordionHeader>
                  <div class="flex items-center gap-3">
                    <ExclamationTriangleIcon class="h-5 w-5 text-red-500" />
                    <span>I can't get through / Call doesn't connect</span>
                  </div>
                </AccordionHeader>
                <AccordionContent>
                  <div class="space-y-3">
                    <p class="text-slate-600">If you're having trouble connecting to the test schedule phone system:</p>
                    <ul class="list-disc list-inside space-y-2 text-slate-600 ml-4">
                      <li>Verify you're calling from your registered phone number</li>
                      <li>Check that you have good cellular or phone service</li>
                      <li>Try calling again in a few minutes</li>
                      <li>Ensure you're dialing the complete number: {{ phoneNumber }}</li>
                    </ul>
                  </div>
                </AccordionContent>
              </AccordionPanel>

              <AccordionPanel value="1">
                <AccordionHeader>
                  <div class="flex items-center gap-3">
                    <KeyIcon class="h-5 w-5 text-orange-500" />
                    <span>I forgot my PIN</span>
                  </div>
                </AccordionHeader>
                <AccordionContent>
                  <div class="space-y-3">
                    <p class="text-slate-600">If you've forgotten your 4-digit PIN:</p>
                    <ul class="list-disc list-inside space-y-2 text-slate-600 ml-4">
                      <li>Contact your healthcare provider to reset your PIN</li>
                      <li>You may need to verify your identity through other means</li>
                      <li>A new PIN will be provided to you securely</li>
                      <li>Do not attempt to guess your PIN as this may lock your account</li>
                    </ul>
                  </div>
                </AccordionContent>
              </AccordionPanel>

              <AccordionPanel value="2">
                <AccordionHeader>
                  <div class="flex items-center gap-3">
                    <ClockIcon class="h-5 w-5 text-yellow-500" />
                    <span>The system hangs up too quickly</span>
                  </div>
                </AccordionHeader>
                <AccordionContent>
                  <div class="space-y-3">
                    <p class="text-slate-600">If the call ends before you can get your schedule information:</p>
                    <ul class="list-disc list-inside space-y-2 text-slate-600 ml-4">
                      <li>Language selection timeout: You have 6 seconds to press 1 or 2</li>
                      <li>PIN entry timeout: You have 10 seconds to enter your PIN and press #</li>
                      <li>Be ready with your PIN before calling</li>
                      <li>Speak clearly if using voice commands</li>
                      <li>Ensure your phone's keypad tones are enabled</li>
                    </ul>
                  </div>
                </AccordionContent>
              </AccordionPanel>

              <AccordionPanel value="3">
                <AccordionHeader>
                  <div class="flex items-center gap-3">
                    <PhoneIcon class="h-5 w-5 text-blue-500" />
                    <span>System says it can't find my account</span>
                  </div>
                </AccordionHeader>
                <AccordionContent>
                  <div class="space-y-3">
                    <p class="text-slate-600">If the system cannot locate your patient account:</p>
                    <ul class="list-disc list-inside space-y-2 text-slate-600 ml-4">
                      <li>Verify you're calling from your registered phone number</li>
                      <li>Check that your phone number is correctly registered in your patient profile</li>
                      <li>Contact your healthcare provider to update your phone number</li>
                      <li>Ensure your patient account is active and properly set up</li>
                    </ul>
                  </div>
                </AccordionContent>
              </AccordionPanel>

              <AccordionPanel value="4">
                <AccordionHeader>
                  <div class="flex items-center gap-3">
                    <GlobeAltIcon class="h-5 w-5 text-green-500" />
                    <span>Language selection issues</span>
                  </div>
                </AccordionHeader>
                <AccordionContent>
                  <div class="space-y-3">
                    <p class="text-slate-600">If you're having trouble with language selection:</p>
                    <ul class="list-disc list-inside space-y-2 text-slate-600 ml-4">
                      <li>Press 1 for English, 2 for Spanish within 6 seconds</li>
                      <li>Make sure your phone's keypad tones are working</li>
                      <li>Press the number firmly and clearly</li>
                      <li>If you miss the selection, the call will end - simply call back</li>
                    </ul>
                  </div>
                </AccordionContent>
              </AccordionPanel>
            </Accordion>
          </template>
        </Card>

        <!-- Contact Information -->
        <Card
          :pt="{
            root: 'bg-gradient-to-r from-emerald-50 to-teal-50 shadow-lg border border-emerald-200',
            content: 'p-6',
          }"
        >
          <template #content>
            <div class="text-center">
              <div class="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <InformationCircleIcon class="h-8 w-8 text-emerald-600" />
              </div>
              <h2 class="text-2xl font-bold text-slate-900 mb-2">Need Additional Help?</h2>
              <p class="text-slate-600 mb-6 max-w-2xl mx-auto">
                If you continue to experience issues with the test schedule phone system or need to update your account information, please contact your healthcare provider directly.
              </p>
              <div class="bg-white p-4 rounded-lg border border-emerald-200 inline-block">
                <div class="flex items-center gap-3">
                  <PhoneIcon class="h-5 w-5 text-emerald-600" />
                  <div class="text-left">
                    <p class="font-semibold text-slate-900">Test Schedule Phone Line</p>
                    <p class="text-emerald-600 font-mono text-lg">{{ phoneNumber }}</p>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </Card>
      </div>
    </div>
  </div>
</template>

<style scoped>
@keyframes bounce-gentle {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

.animate-bounce-gentle {
  animation: bounce-gentle 2s infinite;
}
</style>
